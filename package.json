{"name": "hafaloha-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@stripe/react-stripe-js": "^3.4.0", "@stripe/stripe-js": "^6.0.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "lucide-react": "^0.344.0", "posthog-js": "^1.232.4", "react": "^18.3.1", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.1", "react-responsive": "^10.0.1", "react-router-dom": "^6.22.3", "react-select": "^5.10.0", "react-swipeable": "^7.0.2", "react-window": "^1.8.11", "recharts": "^2.12.2", "xlsx": "^0.18.5", "zustand": "^4.5.6"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.13.11", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "directory-tree": "^3.5.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}