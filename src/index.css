/* src/index.css */

/* Import the Google font from the Ordering side */
@import url('https://fonts.googleapis.com/css2?family=Pacifico&display=swap');

/* Tailwind layers */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom classes from the Ordering side */
.font-display {
  font-family: 'Pacifico', cursive;
}

.tropical-pattern {
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg width='52' height='26' ... %3C/svg%3E");
}

/* Reservation system custom styles */

/* Ensure datepicker calendar is not cut off */
.react-datepicker-popper {
  z-index: 999;
  position: fixed;
}

.react-datepicker__month-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgb(229, 231, 235);
}

.react-datepicker__triangle {
  display: none;
}

/* If Reservations had any special classes, add them here too, or move them
   into domain-specific .css if you prefer. */

/* Scrollable toast container styles - reduce !important usage */
.scrollable-toast-container {
  max-height: 100vh;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  touch-action: pan-y;
}

/* Base styles for all devices */
:root {
  --primary-color: #c1902f;
  --text-color: #111827;
  --background-color: #f9fafb;
  --card-background: #ffffff;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --border-radius: 0.5rem;
  --transition-standard: all 0.2s ease;
}

/* optimize 100vh for ios */
.full-height {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* responsive container */
.responsive-container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Mobile styles */
@media (max-width: 767px) {
  /* Improve touch targets */
  button, a, input[type="button"], input[type="submit"], [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Prevent text zoom on iOS */
  input, select, textarea {
    font-size: 16px;
  }
  
  /* Optimize scrolling */
  * {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Hide scrollbars on mobile but keep functionality */
  .custom-scrollbar::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
  
  /* Adjust spacing for mobile */
  .responsive-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Fix fixed positioning issues with iOS keyboard */
  .fixed-bottom {
    position: sticky;
    bottom: 0;
  }
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1280px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  /* desktop-specific scrollbar styling */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* Add some spacing between toasts for better readability when scrolling */
.scrollable-toast-container > div {
  margin-bottom: 8px;
}

/* Adjust toast width for different screen sizes - use consistent breakpoints */
@media (max-width: 767px) {
  /* Mobile phones - changed from 480px to match other breakpoints */
  .scrollable-toast-container > div > div {
    width: 95% !important;
    max-width: 95vw !important;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  /* Tablets - changed from 768px to match other breakpoints */
  .scrollable-toast-container > div > div {
    width: 90% !important;
    max-width: 400px !important;
    margin-left: auto;
    margin-right: auto;
  }
}
