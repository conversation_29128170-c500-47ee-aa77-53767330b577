<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hafaloha - Offline</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background-color: #f5f5f5;
      color: #333;
      text-align: center;
    }
    
    .container {
      max-width: 600px;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      color: #e74c3c;
      margin-bottom: 1rem;
    }
    
    p {
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }
    
    .logo {
      width: 120px;
      height: auto;
      margin-bottom: 2rem;
    }
    
    .btn {
      display: inline-block;
      background-color: #e74c3c;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
      transition: background-color 0.2s;
    }
    
    .btn:hover {
      background-color: #c0392b;
    }
    
    .icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>You're Offline</h1>
    <p>It looks like you've lost your internet connection. The Hafaloha ordering system requires an internet connection to function properly.</p>
    <p>Please check your connection and try again.</p>
    <a href="/" class="btn">Try Again</a>
  </div>
  
  <script>
    // Check if we're back online
    window.addEventListener('online', () => {
      window.location.reload();
    });
    
    // Try to reload the page when the user clicks the "Try Again" button
    document.querySelector('.btn').addEventListener('click', (e) => {
      e.preventDefault();
      window.location.reload();
    });
  </script>
</body>
</html>
