["", ".bolt", ".bolt/config.json", ".bolt/prompt", ".env", "README.md", "directoryStructure.json", "eslint.config.js", "generateTree.js", "index.html", "package-lock.json", "package.json", "postcss.config.js", "public", "public/_redirects", "public/hafaloha-logo-white-bg.png", "src", "src/GlobalLayout.tsx", "src/RootApp.tsx", "src/index.css", "src/main.tsx", "src/ordering", "src/ordering/OnlineOrderingApp.tsx", "src/ordering/assets", "src/ordering/assets/Hafaloha-circle-logo.png", "src/ordering/assets/hafaloha-logo.png", "src/ordering/assets/hafaloha_hero.jpg", "src/ordering/components", "src/ordering/components/CartPage.tsx", "src/ordering/components/CheckoutPage.tsx", "src/ordering/components/CustomizationModal.tsx", "src/ordering/components/Footer.tsx", "src/ordering/components/Header.tsx", "src/ordering/components/Hero.tsx", "src/ordering/components/LoadingSpinner.tsx", "src/ordering/components/MenuItem.tsx", "src/ordering/components/MenuPage.tsx", "src/ordering/components/OrderConfirmation.tsx", "src/ordering/components/admin", "src/ordering/components/admin/AdminDashboard.tsx", "src/ordering/components/admin/AnalyticsManager.tsx", "src/ordering/components/admin/InventoryManager.tsx", "src/ordering/components/admin/MenuManager.tsx", "src/ordering/components/admin/OrderManager.tsx", "src/ordering/components/admin/PromoManager.tsx", "src/ordering/components/auth", "src/ordering/components/auth/LoginForm.tsx", "src/ordering/components/auth/SignUpForm.tsx", "src/ordering/components/location", "src/ordering/components/location/PickupInfo.tsx", "src/ordering/components/loyalty", "src/ordering/components/loyalty/LoyaltyTeaser.tsx", "src/ordering/components/profile", "src/ordering/components/profile/OrderHistory.tsx", "src/ordering/components/reservation", "src/ordering/components/reservation/ReservationModal.tsx", "src/ordering/components/upsell", "src/ordering/components/upsell/UpsellModal.tsx", "src/ordering/context", "src/ordering/context/AuthContext.tsx", "src/ordering/data", "src/ordering/data/menu.ts", "src/ordering/lib", "src/ordering/lib/api.ts", "src/ordering/store", "src/ordering/store/authStore.ts", "src/ordering/store/inventoryStore.ts", "src/ordering/store/loadingStore.ts", "src/ordering/store/menuStore.ts", "src/ordering/store/notificationStore.ts", "src/ordering/store/orderStore.ts", "src/ordering/store/promoStore.ts", "src/ordering/types", "src/ordering/types/auth.ts", "src/ordering/types/inventory.ts", "src/ordering/types/menu.ts", "src/ordering/types/order.ts", "src/ordering/types/promo.ts", "src/reservations", "src/reservations/ReservationsApp.tsx", "src/reservations/assets", "src/reservations/components", "src/reservations/components/AdminSettings.tsx", "src/reservations/components/FloorManager.tsx", "src/reservations/components/FloorTabs.tsx", "src/reservations/components/HomePage.tsx", "src/reservations/components/LoginPage.tsx", "src/reservations/components/ProfilePage.tsx", "src/reservations/components/RenameSeatsModal.tsx", "src/reservations/components/ReservationForm.tsx", "src/reservations/components/ReservationFormModal.tsx", "src/reservations/components/ReservationModal.tsx", "src/reservations/components/SeatLayoutCanvas.tsx", "src/reservations/components/SeatLayoutEditor.tsx", "src/reservations/components/SeatPreferenceMapModal.tsx", "src/reservations/components/SeatPreferenceWizard.tsx", "src/reservations/components/SignupPage.tsx", "src/reservations/components/StaffDashboard.tsx", "src/reservations/components/WaitlistForm.tsx", "src/reservations/components/dashboard", "src/reservations/components/dashboard/LayoutTab.tsx", "src/reservations/components/dashboard/ReservationsTab.tsx", "src/reservations/components/dashboard/SeatingTab.tsx", "src/reservations/components/dashboard/SettingsTab.tsx", "src/reservations/components/dashboard/WaitlistTab.tsx", "src/reservations/components/modals", "src/reservations/context", "src/reservations/context/AuthContext.tsx", "src/reservations/context/DateFilterContext.tsx", "src/reservations/services", "src/reservations/services/api.ts", "src/reservations/types", "src/reservations/types/index.ts", "src/shared", "src/shared/ScrollToTop.tsx", "src/vite-env.d.ts", "tailwind.config.js", "tsconfig.app.json", "tsconfig.json", "tsconfig.node.json", "vite.config.ts"]